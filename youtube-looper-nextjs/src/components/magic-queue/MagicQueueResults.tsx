'use client'

import { VideoSearchResult } from '@/lib/types/video'
import { useDraftQueue } from '@/hooks/useDraftQueue'
import { youtubeService } from '@/lib/services/youtube'

// Helper function to format duration
function formatDuration(duration: string): string {
  const match = duration.match(/PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/)
  if (!match) return ''

  const hours = parseInt(match[1] || '0')
  const minutes = parseInt(match[2] || '0')
  const seconds = parseInt(match[3] || '0')

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
  } else {
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }
}

// Helper function to format view count
function formatViewCount(count: number): string {
  if (count >= 1000000) {
    return `${(count / 1000000).toFixed(1)}M`
  } else if (count >= 1000) {
    return `${(count / 1000).toFixed(1)}K`
  } else {
    return count.toString()
  }
}

interface MagicQueueResultsProps {
  videos: VideoSearchResult[]
  explanation?: string
  searchQueries?: string[]
  isLoading?: boolean
}

export function MagicQueueResults({ videos, explanation, searchQueries, isLoading }: MagicQueueResultsProps) {
  const { addToDraft, isInDraft, isCreationMode, isEditMode } = useDraftQueue()

  const handleAddToQueue = (video: VideoSearchResult) => {
    try {
      const videoMetadata = youtubeService.searchResultToVideoMetadata(video)

      if (isCreationMode || isEditMode) {
        // Add to draft queue when in creation or edit mode (with default loop count of 1)
        const draftId = addToDraft(videoMetadata)
        if (draftId) {
          console.log('✅ Added AI recommendation to draft queue:', videoMetadata.title, 'Draft ID:', draftId)
        }
      }
    } catch (error) {
      console.error('❌ Failed to add AI recommendation:', error)
    }
  }

  const handleAddAllToQueue = () => {
    videos.forEach(video => {
      handleAddToQueue(video)
    })
  }

  if (isLoading) {
    return (
      <div className="glassmorphism rounded-2xl p-6">
        <div className="flex items-center justify-center space-x-3 py-8">
          <div className="w-6 h-6 border-2 border-primary-400/30 border-t-primary-400 rounded-full animate-spin"></div>
          <span className="text-dark-300">AI is generating your magic queue...</span>
        </div>
      </div>
    )
  }

  if (!videos.length) {
    return null
  }

  return (
    <div className="space-y-6">
      {/* AI Explanation */}
      {explanation && (
        <div className="glassmorphism rounded-2xl p-6">
          <div className="flex items-start space-x-3">
            <div className="w-8 h-8 bg-primary-500 rounded-full flex items-center justify-center flex-shrink-0">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" className="text-white">
                <path d="M7.5 5.6L10 7L8.6 4.5L10 2L7.5 3.4L5 2L6.4 4.5L5 7L7.5 5.6ZM19.5 15.4L22 14L20.6 16.5L22 19L19.5 17.6L17 19L18.4 16.5L17 14L19.5 15.4ZM22 2L20.6 4.5L22 7L19.5 5.6L17 7L18.4 4.5L17 2L19.5 3.4L22 2Z"/>
              </svg>
            </div>
            <div className="flex-1">
              <h3 className="text-lg font-semibold text-white mb-2">AI Curation Results</h3>
              <p className="text-dark-300 leading-relaxed mb-3">{explanation}</p>

              {/* Search Queries Used */}
              {searchQueries && searchQueries.length > 0 && (
                <div className="mt-3">
                  <h4 className="text-sm font-medium text-dark-300 mb-2">Search queries used:</h4>
                  <div className="flex flex-wrap gap-2">
                    {searchQueries.map((query, index) => (
                      <span
                        key={index}
                        className="px-2 py-1 text-xs bg-dark-700/50 text-dark-200 rounded-md border border-dark-600/50"
                      >
                        "{query}"
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Results Header with Add All Button */}
      <div className="glassmorphism rounded-2xl p-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h3 className="text-lg font-semibold text-white">
              Magic Queue Results ({videos.length} videos)
            </h3>
            <p className="text-dark-300 text-sm">
              Real YouTube videos found and curated by AI based on your request
            </p>
          </div>
          <button
            onClick={handleAddAllToQueue}
            className="btn-primary flex items-center space-x-2"
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
            </svg>
            <span>Add All</span>
          </button>
        </div>

        {/* Video Results */}
        <div className="space-y-3">
          {videos.map((video, index) => {
            const hasInDraft = (isCreationMode || isEditMode) && isInDraft(video.id)
            const isDraftMode = isCreationMode || isEditMode

            return (
              <div
                key={`${video.id}-${index}`}
                className="search-result-item group"
              >
                {/* Thumbnail */}
                <div className="flex-shrink-0">
                  <img
                    src={video.thumbnail.url}
                    alt={video.title}
                    className="w-20 h-14 object-cover rounded-lg bg-dark-700"
                    onError={(e) => {
                      // Fallback to play icon if thumbnail fails to load
                      const target = e.target as HTMLImageElement
                      target.style.display = 'none'
                      target.nextElementSibling?.classList.remove('hidden')
                    }}
                  />
                  <div className="w-20 h-14 bg-dark-700 rounded-lg flex items-center justify-center hidden">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor" className="text-dark-400">
                      <path d="M8 5v14l11-7z"/>
                    </svg>
                  </div>
                </div>

                {/* Video Info */}
                <div className="flex-1 min-w-0">
                  <h4 className="font-medium text-white truncate group-hover:text-primary-300 transition-colors duration-200">
                    {video.title}
                  </h4>
                  <p className="text-sm text-dark-300 truncate">
                    {video.channel.title}
                  </p>
                  {video.description && (
                    <p className="text-xs text-dark-400 mt-1 line-clamp-2">
                      {video.description}
                    </p>
                  )}
                  {/* Duration and View Count */}
                  <div className="flex items-center space-x-2 mt-1 text-xs text-dark-400">
                    {video.duration && video.duration !== 'PT0S' && (
                      <span>{formatDuration(video.duration)}</span>
                    )}
                    {video.viewCount && (
                      <span>• {formatViewCount(video.viewCount)} views</span>
                    )}
                  </div>
                </div>

                {/* Add Button */}
                <div className="flex-shrink-0">
                  <button
                    onClick={() => handleAddToQueue(video)}
                    className="p-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 btn-primary"
                    title={isDraftMode ? (hasInDraft ? 'Add Another' : 'Add to Draft') : 'Add to Queue'}
                  >
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
                    </svg>
                  </button>
                </div>
              </div>
            )
          })}
        </div>

        {/* Note about AI curation */}
        <div className="mt-4 p-3 bg-dark-800/50 rounded-lg border border-dark-700/50">
          <p className="text-xs text-dark-400">
            <span className="text-primary-400">🤖 AI Process:</span> AI generated search queries,
            found real YouTube videos, and selected the best matches for your request.
            These are actual videos with real thumbnails, durations, and metadata.
          </p>
        </div>
      </div>
    </div>
  )
}
