'use client'

import { useState } from 'react'

interface MagicPromptInputProps {
  onSubmit: (prompt: string, count: number, maxDuration?: number) => void
  isLoading: boolean
  placeholder?: string
}

export function MagicPromptInput({ onSubmit, isLoading, placeholder }: MagicPromptInputProps) {
  const [prompt, setPrompt] = useState('')
  const [count, setCount] = useState(10)
  const [maxDuration, setMaxDuration] = useState<number | undefined>(undefined)
  const [enableDurationFilter, setEnableDurationFilter] = useState(false)

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (prompt.trim() && !isLoading) {
      onSubmit(prompt.trim(), count, enableDurationFilter ? maxDuration : undefined)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSubmit(e)
    }
  }

  const examplePrompts = [
    "Educational videos about Programming",
    "Teach me something new",
    "Relaxing nature documentaries",
    "Beginner guitar tutorials",
    "History of ancient civilizations",
    "Cooking healthy meals",
    "Space exploration documentaries",
    "Learn a new language"
  ]

  const handleExampleClick = (example: string) => {
    setPrompt(example)
  }

  return (
    <div className="space-y-6">
      {/* Main Input Form */}
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="relative">
          <textarea
            value={prompt}
            onChange={(e) => setPrompt(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder={placeholder || "Describe what kind of videos you'd like to watch..."}
            className="input-field min-h-[120px] resize-none pr-16"
            disabled={isLoading}
            rows={4}
          />
          
          {/* Magic wand icon in textarea */}
          <div className="absolute top-4 right-4 text-primary-400">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
              <path d="M7.5 5.6L10 7L8.6 4.5L10 2L7.5 3.4L5 2L6.4 4.5L5 7L7.5 5.6ZM19.5 15.4L22 14L20.6 16.5L22 19L19.5 17.6L17 19L18.4 16.5L17 14L19.5 15.4ZM22 2L20.6 4.5L22 7L19.5 5.6L17 7L18.4 4.5L17 2L19.5 3.4L22 2ZM13.34 12.78L15.78 10.34L13.66 8.22L11.22 10.66L13.34 12.78ZM14.37 7.29L16.71 9.63C17.1 10.02 17.1 10.65 16.71 11.04L11.04 16.71C10.65 17.1 10.02 17.1 9.63 16.71L7.29 14.37C6.9 13.98 6.9 13.35 7.29 12.96L12.96 7.29C13.35 6.9 13.98 6.9 14.37 7.29Z"/>
            </svg>
          </div>
        </div>

        {/* Configuration Options */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Video Count */}
          <div>
            <label className="block text-sm font-medium text-dark-300 mb-2">
              Number of videos
            </label>
            <select
              value={count}
              onChange={(e) => setCount(parseInt(e.target.value))}
              disabled={isLoading}
              className="input-field"
            >
              <option value={5}>5 videos</option>
              <option value={10}>10 videos</option>
              <option value={15}>15 videos</option>
              <option value={20}>20 videos</option>
            </select>
          </div>

          {/* Duration Filter */}
          <div>
            <label className="block text-sm font-medium text-dark-300 mb-2">
              Max duration (optional)
            </label>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="enableDuration"
                  checked={enableDurationFilter}
                  onChange={(e) => setEnableDurationFilter(e.target.checked)}
                  disabled={isLoading}
                  className="w-4 h-4 text-primary-600 bg-dark-700 border-dark-600 rounded focus:ring-primary-500"
                />
                <label htmlFor="enableDuration" className="text-sm text-dark-300">
                  Filter by duration
                </label>
              </div>
              {enableDurationFilter && (
                <select
                  value={maxDuration || 30}
                  onChange={(e) => setMaxDuration(parseInt(e.target.value))}
                  disabled={isLoading}
                  className="input-field"
                >
                  <option value={5}>Up to 5 minutes</option>
                  <option value={10}>Up to 10 minutes</option>
                  <option value={20}>Up to 20 minutes</option>
                  <option value={30}>Up to 30 minutes</option>
                  <option value={60}>Up to 1 hour</option>
                </select>
              )}
            </div>
          </div>
        </div>

        <button
          type="submit"
          disabled={!prompt.trim() || isLoading}
          className="btn-primary w-full flex items-center justify-center space-x-2 py-3"
        >
          {isLoading ? (
            <>
              <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
              <span>Creating Magic Queue...</span>
            </>
          ) : (
            <>
              <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                <path d="M7.5 5.6L10 7L8.6 4.5L10 2L7.5 3.4L5 2L6.4 4.5L5 7L7.5 5.6ZM19.5 15.4L22 14L20.6 16.5L22 19L19.5 17.6L17 19L18.4 16.5L17 14L19.5 15.4ZM22 2L20.6 4.5L22 7L19.5 5.6L17 7L18.4 4.5L17 2L19.5 3.4L22 2ZM13.34 12.78L15.78 10.34L13.66 8.22L11.22 10.66L13.34 12.78ZM14.37 7.29L16.71 9.63C17.1 10.02 17.1 10.65 16.71 11.04L11.04 16.71C10.65 17.1 10.02 17.1 9.63 16.71L7.29 14.37C6.9 13.98 6.9 13.35 7.29 12.96L12.96 7.29C13.35 6.9 13.98 6.9 14.37 7.29Z"/>
              </svg>
              <span>Generate Magic Queue</span>
            </>
          )}
        </button>
      </form>

      {/* Example Prompts */}
      <div className="space-y-3">
        <h4 className="text-sm font-medium text-dark-300">Try these examples:</h4>
        <div className="flex flex-wrap gap-2">
          {examplePrompts.map((example, index) => (
            <button
              key={index}
              onClick={() => handleExampleClick(example)}
              disabled={isLoading}
              className="px-3 py-1.5 text-sm bg-dark-700/50 hover:bg-dark-600/50 text-dark-200 hover:text-white rounded-lg transition-all duration-200 border border-dark-600/50 hover:border-dark-500/50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {example}
            </button>
          ))}
        </div>
      </div>

      {/* Character count */}
      <div className="text-right">
        <span className={`text-xs ${prompt.length > 500 ? 'text-red-400' : 'text-dark-400'}`}>
          {prompt.length}/500
        </span>
      </div>
    </div>
  )
}
