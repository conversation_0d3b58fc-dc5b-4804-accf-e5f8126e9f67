'use client'

import { useState } from 'react'
import { useQueue } from '@/hooks/useQueue'
import { useAuth } from '@/hooks/useAuth'
import { useToast } from '@/components/providers/ToastProvider'

export function ShareQueueButton() {
  const [isSharing, setIsSharing] = useState(false)
  const [shareLink, setShareLink] = useState('')
  const [showLink, setShowLink] = useState(false)
  const { items, shareCurrentQueue } = useQueue()
  const { isAuthenticated } = useAuth()
  const { showToast } = useToast()

  const handleShareQueue = async () => {
    if (items.length === 0) {
      showToast('Cannot share an empty queue', 'warning')
      return
    }

    if (!isAuthenticated) {
      showToast('Please sign in to share queues', 'warning')
      return
    }

    setIsSharing(true)

    try {
      // Create a temporary title for sharing
      const title = `Shared Queue - ${new Date().toLocaleDateString()}`
      const publicQueueId = await shareCurrentQueue(title)

      if (publicQueueId) {
        const link = `${window.location.origin}?q=${publicQueueId}`
        setShareLink(link)
        setShowLink(true)

        // Copy to clipboard
        try {
          await navigator.clipboard.writeText(link)
          console.log('✅ Share link copied to clipboard')
          showToast('Queue shared! Link copied to clipboard', 'success')
        } catch (error) {
          console.warn('Could not copy to clipboard:', error)
          showToast('Queue shared successfully', 'success')
        }
      }
    } catch (error) {
      console.error('❌ Failed to share queue:', error)
      showToast('Failed to share queue. Please try again.', 'error')
    } finally {
      setIsSharing(false)
    }
  }

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(shareLink)
      console.log('✅ Link copied to clipboard')
      showToast('Link copied to clipboard', 'success')
    } catch (error) {
      console.warn('Could not copy to clipboard:', error)
      showToast('Could not copy to clipboard', 'error')
    }
  }

  if (items.length === 0) {
    return null
  }

  return (
    <div className="flex items-center space-x-2">
      <button
        onClick={handleShareQueue}
        disabled={isSharing || !isAuthenticated}
        className="btn-primary text-sm px-3 py-1 flex items-center space-x-1 disabled:opacity-50 disabled:cursor-not-allowed"
        title={!isAuthenticated ? "Sign in to share queues" : "Share this queue"}
      >
        {isSharing ? (
          <>
            <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
            <span>Sharing...</span>
          </>
        ) : (
          <>
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81 1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3c0 .***********.7L8.04 9.81C7.5 9.31 6.79 9 6 9c-1.66 0-3 1.34-3 3s1.34 3 3 3c.79 0 1.50-.31 2.04-.81l7.12 4.16c-.05.21-.08.43-.08.65 0 1.61 1.31 2.92 2.92 2.92s2.92-1.31 2.92-2.92-1.31-2.92-2.92-2.92z"/>
            </svg>
          </>
        )}
      </button>

      {showLink && (
        <div className="flex items-center space-x-2 bg-dark-800 rounded-lg px-3 py-2">
          <input
            type="text"
            value={shareLink}
            readOnly
            className="bg-transparent text-white text-sm w-64 outline-none"
          />
          <button
            onClick={copyToClipboard}
            className="text-primary-400 hover:text-primary-300 transition-colors"
            title="Copy link"
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
            </svg>
          </button>
          <button
            onClick={() => setShowLink(false)}
            className="text-dark-400 hover:text-white transition-colors"
            title="Close"
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
            </svg>
          </button>
        </div>
      )}
    </div>
  )
}
