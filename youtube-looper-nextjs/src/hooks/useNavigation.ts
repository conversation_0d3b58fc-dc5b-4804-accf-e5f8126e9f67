'use client'

import { createContext, useContext } from 'react'

export type NavigationView = 'search' | 'magic' | 'personal' | 'public'

export interface NavigationContextType {
  activeView: NavigationView
  setActiveView: (view: NavigationView) => void
}

export const NavigationContext = createContext<NavigationContextType>({
  activeView: 'search',
  setActiveView: () => {},
})

export const useNavigation = () => {
  const context = useContext(NavigationContext)
  if (!context) {
    throw new Error('useNavigation must be used within a NavigationProvider')
  }
  return context
}
