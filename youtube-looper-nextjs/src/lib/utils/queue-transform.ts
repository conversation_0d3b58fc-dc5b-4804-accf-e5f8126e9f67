import { QueueState } from '@/lib/types/queue'
import { VideoLoopSettings } from '@/lib/types/video'

/**
 * Transform queue data to ensure proper QueueState structure
 * Expects all queue items to have the complete QueueItem structure
 */
export function transformQueueData(queueData: any): QueueState {
  // Expect items to already have the correct structure
  const items = queueData.items || []

  // Transform queue data structure
  return {
    items: items.map((item: any, index: number) => ({
      ...item,
      queueIndex: index, // Ensure correct queue index
      addedAt: item.addedAt || Date.now(),
      url: item.url || `https://www.youtube.com/watch?v=${item.id}`,
      duration: item.duration || 0,
      // Ensure required fields exist
      timeframes: item.timeframes || [],
      loopSettings: item.loopSettings || {
        videoLoopCount: 1,
        loopMode: 'whole-video-plus-timeframes' as const
      }
    })),
    currentIndex: queueData.currentIndex || 0,
    isPlaying: false, // Don't auto-play loaded queues
    queueLoopCount: queueData.queueLoopCount !== undefined ? queueData.queueLoopCount : -1,
    shuffle: queueData.shuffle || false,
    volume: queueData.volume || 1,
    timestamp: Date.now()
  }
}
