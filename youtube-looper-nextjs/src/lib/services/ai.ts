// Firebase AI Logic service for generating video recommendations

import { getAI, getGenerativeModel, GoogleAIBackend } from 'firebase/ai'
import { getFirebaseApp } from '@/lib/firebase/config'
import { VideoSearchResult } from '@/lib/types/video'
import { youtubeService } from './youtube'

export interface MagicQueueRequest {
  prompt: string
  count?: number
  maxDuration?: number // in minutes, optional filter
}

export interface MagicQueueResponse {
  videos: VideoSearchResult[]
  explanation?: string
  searchQueries?: string[] // The queries that were used
}

export class AIService {
  private getAI() {
    const app = getFirebaseApp()
    if (!app) {
      throw new Error('Firebase not initialized. AI service will not work.')
    }
    
    try {
      return getAI(app, { backend: new GoogleAIBackend() })
    } catch (error) {
      console.error('❌ Error initializing Firebase AI:', error)
      throw new Error('Firebase AI not configured properly. Please check your Firebase AI Logic setup.')
    }
  }

  private getModel() {
    const ai = this.getAI()
    return getGenerativeModel(ai, { model: 'gemini-2.5-flash' })
  }

  /**
   * Generate search queries based on user prompt
   */
  private async generateSearchQueries(prompt: string, count: number): Promise<string[]> {
    const model = this.getModel()

    const systemPrompt = `You are a YouTube search expert. Based on the user's request, generate ${Math.ceil(count / 2)} different search queries that would find the best videos for their needs.

IMPORTANT: You must respond with a valid JSON array of search queries:
["search query 1", "search query 2", "search query 3"]

Guidelines:
- Create diverse search queries that cover different aspects of the request
- Use terms that are commonly used on YouTube
- Include both broad and specific queries
- Focus on findable, popular content
- Avoid overly complex or niche terms

User request: "${prompt}"`

    const result = await model.generateContent(systemPrompt)
    const responseText = result.response.text()

    try {
      // Extract JSON from response if it's wrapped in markdown code blocks
      const jsonMatch = responseText.match(/```(?:json)?\s*([\s\S]*?)\s*```/)
      const jsonText = jsonMatch ? jsonMatch[1] : responseText
      const queries = JSON.parse(jsonText)

      if (!Array.isArray(queries)) {
        throw new Error('AI response is not an array')
      }

      return queries.filter(q => typeof q === 'string' && q.trim().length > 0)
    } catch (parseError) {
      console.error('❌ Failed to parse search queries:', parseError)
      // Fallback: create basic search queries from the prompt
      return [prompt, `${prompt} tutorial`, `${prompt} guide`]
    }
  }

  /**
   * Select best videos from search results using AI
   */
  private async selectBestVideos(
    prompt: string,
    allVideos: VideoSearchResult[],
    count: number,
    maxDuration?: number
  ): Promise<{ videos: VideoSearchResult[], explanation: string }> {
    const model = this.getModel()

    // Filter by duration if specified
    let filteredVideos = allVideos
    if (maxDuration) {
      filteredVideos = allVideos.filter(video => {
        const duration = this.parseDurationToMinutes(video.duration)
        return duration <= maxDuration
      })
    }

    // If we have fewer videos than requested, just return all
    if (filteredVideos.length <= count) {
      return {
        videos: filteredVideos,
        explanation: `Found ${filteredVideos.length} videos matching your criteria.`
      }
    }

    // Prepare video data for AI analysis
    const videoData = filteredVideos.map((video, index) => ({
      index,
      title: video.title,
      channel: video.channel.title,
      description: video.description.substring(0, 200), // Limit description length
      duration: video.duration,
      viewCount: video.viewCount || 0
    }))

    const systemPrompt = `You are a video curation expert. From the following ${filteredVideos.length} YouTube videos, select the ${count} BEST videos that match this user request: "${prompt}"

Videos to choose from:
${JSON.stringify(videoData, null, 2)}

IMPORTANT: Respond with a valid JSON object:
{
  "selectedIndices": [0, 2, 5, 7, 9],
  "explanation": "Brief explanation of why these videos were selected"
}

Selection criteria:
- Relevance to the user's request
- Video quality indicators (views, channel reputation)
- Diversity of content and perspectives
- Educational or entertainment value
- Avoid duplicates or very similar content

Select exactly ${count} videos by their index numbers.`

    const result = await model.generateContent(systemPrompt)
    const responseText = result.response.text()

    try {
      // Extract JSON from response
      const jsonMatch = responseText.match(/```(?:json)?\s*([\s\S]*?)\s*```/)
      const jsonText = jsonMatch ? jsonMatch[1] : responseText
      const selection = JSON.parse(jsonText)

      if (!selection.selectedIndices || !Array.isArray(selection.selectedIndices)) {
        throw new Error('Invalid selection format')
      }

      // Get selected videos
      const selectedVideos = selection.selectedIndices
        .filter((index: number) => index >= 0 && index < filteredVideos.length)
        .slice(0, count) // Ensure we don't exceed requested count
        .map((index: number) => filteredVideos[index])

      return {
        videos: selectedVideos,
        explanation: selection.explanation || `Selected ${selectedVideos.length} best videos for your request.`
      }
    } catch (parseError) {
      console.error('❌ Failed to parse video selection:', parseError)
      // Fallback: return first N videos
      return {
        videos: filteredVideos.slice(0, count),
        explanation: `Selected the first ${count} videos from search results.`
      }
    }
  }

  /**
   * Parse YouTube duration to minutes
   */
  private parseDurationToMinutes(duration: string): number {
    // Parse ISO 8601 duration format (PT1H2M3S)
    const match = duration.match(/PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/)
    if (!match) return 0

    const hours = parseInt(match[1] || '0')
    const minutes = parseInt(match[2] || '0')
    const seconds = parseInt(match[3] || '0')

    return hours * 60 + minutes + seconds / 60
  }

  /**
   * Generate video recommendations based on user prompt
   */
  async generateVideoRecommendations(request: MagicQueueRequest): Promise<MagicQueueResponse> {
    const { prompt, count = 10, maxDuration } = request

    try {
      console.log('🪄 Generating magic queue for prompt:', prompt)
      console.log('📊 Parameters:', { count, maxDuration })

      // Step 1: Generate search queries using AI
      console.log('🔍 Step 1: Generating search queries...')
      const searchQueries = await this.generateSearchQueries(prompt, count)
      console.log('✅ Generated search queries:', searchQueries)

      // Step 2: Search YouTube for each query
      console.log('🔍 Step 2: Searching YouTube...')
      const allVideos: VideoSearchResult[] = []

      for (const query of searchQueries) {
        try {
          console.log(`  Searching for: "${query}"`)
          const results = await youtubeService.searchVideos(query, Math.ceil(count * 1.5)) // Get more results for better selection
          allVideos.push(...results)
        } catch (error) {
          console.warn(`Failed to search for "${query}":`, error)
          // Continue with other queries
        }
      }

      // Remove duplicates by video ID
      const uniqueVideos = allVideos.filter((video, index, self) =>
        self.findIndex(v => v.id === video.id) === index
      )

      console.log(`✅ Found ${uniqueVideos.length} unique videos from ${allVideos.length} total results`)

      if (uniqueVideos.length === 0) {
        throw new Error('No videos found for your request. Try a different prompt.')
      }

      // Step 3: Use AI to select the best videos
      console.log('🤖 Step 3: AI selecting best videos...')
      const selection = await this.selectBestVideos(prompt, uniqueVideos, count, maxDuration)

      console.log(`✅ Selected ${selection.videos.length} best videos`)

      return {
        videos: selection.videos,
        explanation: selection.explanation,
        searchQueries
      }

    } catch (error) {
      console.error('❌ Error generating video recommendations:', error)

      if (error instanceof Error) {
        throw error
      }

      throw new Error('Failed to generate video recommendations. Please try again.')
    }
  }

  /**
   * Check if Firebase AI Logic is properly configured
   */
  async checkAIConfiguration(): Promise<boolean> {
    try {
      const model = this.getModel()
      // Try a simple test request
      await model.generateContent('Test')
      return true
    } catch (error) {
      console.error('❌ AI configuration check failed:', error)
      return false
    }
  }
}

// Export singleton instance
export const aiService = new AIService()
